import React from "react";
import "./LandingPage.css";
import Navbar from "../layouts/Navbar";
import Hero from "../layouts/Hero";
import About from "../layouts/About";
import Projects from "../layouts/Projects";
import Experience from "../layouts/Experience";
import Contact from "../layouts/Contact";
import Footer from "../layouts/Footer";

const LandingPage = () => {
  // Mock data
  const personalInfo = {
    name: "<PERSON>",
    title: "Full Stack Developer",
    tagline: "Building digital experiences that make a difference",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    profileImage:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
  };

  const skills = [
    "JavaScript",
    "React",
    "Node.js",
    "Python",
    "TypeScript",
    "MongoDB",
    "PostgreSQL",
    "AWS",
    "Docker",
    "Git",
  ];

  const projects = [
    {
      id: 1,
      title: "DAT Freight & Analytics",
      description:
        "US-based freight exchange service and provider of transportation information serving North America. Worked as a Full-Stack dev, updating and improving their current API, adding new user flows, fixing the responsive design, allowing the users to select the best freight forwarder that suits their needs. Also improved their error handling and monitoring with Sentry",
      technologies: [
        "Angular",
        "NodeJS",
        "Sass",
        "Sentry",
        "MySQL",
        "Docker",
        "MongoDB",
        "AWS Batch",
      ],
      image:
        "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=250&fit=crop",
      liveUrl: "https://www.dat.com/",
      githubUrl: "#",
    },
    {
      id: 2,
      title: "Compare Credit",
      description:
        "Company that specializes in credit card comparison services. On the frontend, developed and maintained a website that enables users to compare credit cards options from multiple providers. On the backend, utilized Node, Sanity CMS, Google BigQuery for the database service, and integrated Sentry for error monitoring and Segment for analytics and tracking.",
      technologies: [
        "Jest",
        "NextJS",
        "TailwindCSS",
        "Node",
        "React",
        "Lodash",
        "Sanity",
        "Google BigQuery",
        "Sentry",
        "Segment",
      ],
      image:
        "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=250&fit=crop",
      liveUrl: "https://comparecredit.com/",
      githubUrl: "#",
    },
    {
      id: 3,
      title: "BusinessLoans",
      description:
        "Website that offers comparisons between different loans from several companies. The user has to fill a short form about themselves, after which it is presented with a selection of loan offers that they can evaluate and choose the best fit. Uses NextJs has it main technology, is highly integrated with their CMS using Sanity, and also connects to Google Cloud Big Query for the Database service",
      technologies: [
        "NextJs",
        "Node",
        "React",
        "Typescript",
        "Jest",
        "TailwindCSS",
        "Google Cloud Big Query",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "https://businessloans.com/",
      githubUrl: "#",
    },
    {
      id: 4,
      title: "IQVIA Salesforce Application",
      description:
        "Application created and hosted on Salesforce platform, using Lightning Web Components (LWC) for reusability and better code delegation. Created custom LWC to fit the clients needs, updating existing LWC, fixing bugs, changing styles according to the UI/UX team proposals, creating new pages and applications flows following the designers Zeplin assets.",
      technologies: [
        "Javascript",
        "SASS",
        "Lightning Web Components",
        "Lightning Design System",
        "CSS",
        "NodeJS",
      ],
      image:
        "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop",
      liveUrl: "https://ivp-dit0.my.salesforce.com/",
      githubUrl: "#",
    },
    {
      id: 5,
      title: "Gdobo CLI Tool",
      description:
        "Command Line Interface (CLI) tool written in Nodejs, which connects to the Google Drive API to compare the files between a local folder and a folder in Google Drive. Uses the Nodejs Stream API to read and write the files. Allows the user to upload, download, remove and update the files both locally and on Google Drive storage.",
      technologies: [
        "NodeJs",
        "Javascript",
        "Google Drive API",
        "Google OAuth API",
        "Bluebird",
        "Lodash",
      ],
      image:
        "https://images.unsplash.com/photo-1629654297299-c8506221ca97?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "https://github.com/ZoiloGranda/gdobo",
    },
    {
      id: 6,
      title: "Connexient Hospital Navigation",
      description:
        "Around 50 Web applications for indoor hospitals navigation and exploration, find places, get step by step directions and many other features. Worked on the front-end team, creating new releases of the web and kiosks apps for new hospitals, fixing bugs and adding new features on existing projects. The web app is written on vanilla javascript, and for the navigation we used the VisioGlobe products solutions.",
      technologies: [
        "Javascript",
        "JQuery",
        "VisioGlobe Web & Kiosk",
        "Google Maps API",
        "jsPDF",
        "Less",
        "Bootstrap",
      ],
      image:
        "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=250&fit=crop",
      liveUrl: "https://capefear-dev.connexient.com/web/",
      githubUrl: "#",
    },
    {
      id: 7,
      title: "Full Stack Developer - Agence - Pentafinanciero",
      description:
        "Old web site created in PHP, which was tasked to the team of developers to migrate it to Nodejs and Angular. I was assigned tasks such as testing all endpoints of the existing SOAP API for consistency, migrating views and logic using Angular, migrating the SOAP API to Restful, creating database models and drivers, implement unit and integration tests and many other tasks for the migration.",
      technologies: [
        "Nodejs",
        "Express",
        "Angular",
        "JQuery",
        "Bootstrap",
        "Mocha",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "https://www.pentafinanciero.cl/",
      githubUrl: "#",
    },
    {
      id: 8,
      title: "Full Stack Developer - Agence - Latam Airlines",
      description:
        "Separate project for Latam Airlines, used to create an inventory and management service for their airplanes parts and maintenance. Was created as a Single Page Application with an authentication module which used OAuth. There were different users profiles and privileges, the users were able to use the standard CRUD operations to check and request parts, and also check the status of the work being done on the airplanes. Socket.io was used to create a live chat between the logged users and notify when there were operations running on the Database. A Cronjob was runing on the backend updating the database with information loaded from a Excel spreadsheet.",
      technologies: [
        "Nodejs",
        "Sails",
        "MySQL",
        "Angular",
        "Angular Material",
        "RESTfulAPI",
        "OAuth",
        "Socket.io",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "https://www.latam.com/es_un/",
      githubUrl: "#",
    },
    {
      id: 9,
      title: "Full Stack Developer - Agence - Latam Airlines",
      description:
        "For this project i was in charge of several fixes and adjustments in front and back end, such as creation of mutation endpoints in GraphQL, adjustments to the existing dynamic views in Angular, generation of reports in excel with information extracted from the database, optimizations in the use of lodash in front end/back end, creation of automated tasks in the server to load information into the database, among others.",
      technologies: [
        "Nodejs",
        "Express",
        "PostgreSQL",
        "Angular",
        "Angular Material",
        "GraphQL",
        "Gulp",
        "Lodash",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "https://www.latam.com/es_un/",
      githubUrl: "#",
    },
    {
      id: 10,
      title: "Full Stack Developer - Develup - Laboratoire",
      description:
        "Development of an administrative system for a veterinary laboratory, in which I was in charge of developing the user interface using Angularjs and Angular Material, consuming an Api developed in Node.js, following the style guide of Jhon Papa, consolidating the development style in the whole project and improving the modularization of the application. I was also in charge of certain parts of the back end, such as the MongoDB document models, the CRUD routes of the application and the development of Mongoose controllers",
      technologies: [
        "Nodejs",
        "Express",
        "MongoDB",
        "Angular",
        "Angular Material",
        "Sass",
        "Cucumber",
      ],
      image:
        "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "#",
    },
  ];

  const experience = [
    {
      id: 1,
      company: "TechCorp Solutions",
      position: "Senior Full Stack Developer",
      duration: "2022 - Present",
      description: "Lead development of web applications serving 100k+ users",
    },
    {
      id: 2,
      company: "StartupXYZ",
      position: "Frontend Developer",
      duration: "2020 - 2022",
      description:
        "Built responsive web applications using React and modern JavaScript",
    },
    {
      id: 3,
      company: "Digital Agency",
      position: "Junior Developer",
      duration: "2019 - 2020",
      description:
        "Developed client websites and learned full-stack development",
    },
  ];

  return (
    <div className="landing-page">
      <Navbar personalInfo={personalInfo} />

      <Hero personalInfo={personalInfo} />

      <About skills={skills} />

      <Projects projects={projects} />

      <Experience experience={experience} />

      <Contact personalInfo={personalInfo} />

      <Footer personalInfo={personalInfo} />
    </div>
  );
};

export default LandingPage;
